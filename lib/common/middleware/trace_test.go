package middleware

import (
	"context"
	"fmt"
	"testing"

	"github.com/getsentry/sentry-go"
)

func TestTraceIDZeroValue(t *testing.T) {
	// Test what happens with an empty/zero TraceID
	var zeroTraceID sentry.TraceID
	fmt.Printf("Zero TraceID String(): '%s'\n", zeroTraceID.String())
	fmt.Printf("Zero TraceID length: %d\n", len(zeroTraceID.String()))
	
	// Test with a real span
	ctx := context.Background()
	transaction := sentry.StartTransaction(ctx, "test-transaction")
	defer transaction.Finish()
	
	span := transaction.StartChild("test-span")
	defer span.Finish()
	
	fmt.Printf("Real TraceID String(): '%s'\n", span.TraceID.String())
	fmt.Printf("Real TraceID length: %d\n", len(span.TraceID.String()))
	
	// Test the actual logic from the code
	if zeroTraceID.String() != "00000000000000000000000000000000" {
		t.Errorf("Expected zero TraceID to be '00000000000000000000000000000000', got '%s'", zeroTraceID.String())
	}
	
	if span.TraceID.String() == "00000000000000000000000000000000" {
		t.Errorf("Expected real TraceID to NOT be zero value, got '%s'", span.TraceID.String())
	}
}
